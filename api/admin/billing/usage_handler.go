package billing

import (
	"fmt"
	"net/http"

	"git.uozi.org/uozi/potato-billing-api/internal/billing"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

// RegisterUsageAPI 初始化用量API
func RegisterUsageAPI(g *gin.RouterGroup) {
	g.GET("/apps/:app_id/usage", GetAppUsageHistory)
	g.GET("/apps/:app_id/stats", GetAppUsageStats)
	g.GET("/usage/overview", GetSystemUsageOverview)
}

// GetAppUsageHistory 获取用量历史
func GetAppUsageHistory(c *gin.Context) {
	appID := c.Param("app_id")
	if appID == "" {
		cosy.ErrHandler(c, fmt.Errorf("应用ID参数必需"))
		return
	}

	// 解析查询参数
	var query billing.UsageHistoryQuery
	query.AppID = appID
	if err := c.ShouldBindQuery(&query); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 获取用量服务
	usageService := billing.NewUsageService()

	// 调用 internal 模块获取用量历史
	response, err := usageService.GetAppUsageHistory(c.Request.Context(), &query)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetAppUsageStats 获取用量统计
func GetAppUsageStats(c *gin.Context) {
	appID := c.Param("app_id")
	if appID == "" {
		cosy.ErrHandler(c, fmt.Errorf("应用ID参数必需"))
		return
	}

	period := c.DefaultQuery("period", "month")

	// 获取用量服务
	usageService := billing.NewUsageService()

	// 调用 internal 模块获取用量统计
	stats, err := usageService.GetAppUsageStats(c.Request.Context(), appID, period)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetSystemUsageOverview 获取系统用量概览
func GetSystemUsageOverview(c *gin.Context) {
	// 获取用量服务
	usageService := billing.NewUsageService()

	// 调用 internal 模块获取系统用量概览
	overview, err := usageService.GetSystemUsageOverview(c.Request.Context())
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, overview)
}
